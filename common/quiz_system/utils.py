"""
Утилиты для quiz системы
Функции очистки сообщений, данных и мониторинга состояния
"""

from aiogram import Bot
import logging
import asyncio
import time
from typing import Dict

from .state_manager import active_questions, completed_questions


async def cleanup_test_messages(chat_id: int, data: dict, bot: Bot):
    """Удаление всех сообщений теста"""
    start_time = time.time()

    try:
        messages_to_delete = data.get("messages_to_delete", [])

        if not messages_to_delete:
            return

        # Удаляем сообщения пакетами для ускорения
        deleted_count = 0
        batch_size = 10

        for i in range(0, len(messages_to_delete), batch_size):
            batch = messages_to_delete[i:i + batch_size]

            # Создаем задачи для параллельного удаления
            tasks = []
            for message_id in batch:
                task = asyncio.create_task(delete_message_safe(bot, chat_id, message_id))
                tasks.append(task)

            # Ждем завершения всех задач в пакете
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Подсчитываем успешные удаления
            for result in results:
                if result is True:
                    deleted_count += 1

            # Минимальная задержка между пакетами для избежания rate limit
            if i + batch_size < len(messages_to_delete):
                await asyncio.sleep(0.01)

        end_time = time.time()
        duration = end_time - start_time

    except Exception as e:
        logging.error(f"❌ QUIZ: Ошибка при удалении сообщений теста: {e}")


async def delete_message_safe(bot: Bot, chat_id: int, message_id: int) -> bool:
    """Безопасное удаление сообщения"""
    try:
        await bot.delete_message(chat_id=chat_id, message_id=message_id)
        return True
    except Exception as e:
        logging.debug(f"QUIZ: Не удалось удалить сообщение {message_id}: {e}")
        return False


async def cleanup_orphaned_quiz_states():
    """Очистка зависших состояний quiz после перезагрузки системы"""
    try:
        # Очищаем все активные вопросы (они потеряли актуальность после перезагрузки)
        active_questions.clear()
        completed_questions.clear()

    except Exception as e:
        logging.error(f"❌ QUIZ: Ошибка при очистке зависших состояний: {e}")


async def cleanup_test_data(user_id: int):
    """Очистка данных завершенного теста"""
    try:
        # Очищаем активные вопросы для этого пользователя
        questions_to_remove = []
        for question_uuid, question_info in active_questions.items():
            if question_info.get("chat_id") == user_id:
                questions_to_remove.append(question_uuid)

        for question_uuid in questions_to_remove:
            del active_questions[question_uuid]

        # Очищаем старые завершенные вопросы (оставляем только последние 100)
        if len(completed_questions) > 100:
            completed_list = list(completed_questions)
            completed_questions.clear()
            completed_questions.update(completed_list[-50:])

    except Exception as e:
        logging.error(f"❌ QUIZ: Ошибка при очистке данных теста: {e}")


def get_active_questions_count() -> int:
    """Получить количество активных вопросов (для мониторинга)"""
    return len(active_questions)


def get_completed_questions_count() -> int:
    """Получить количество завершенных вопросов (для мониторинга)"""
    return len(completed_questions)
